(()=>{var e={};e.id=222,e.ids=[222],e.modules={3070:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var r=a(60687),s=a(43210),n=a(16189),i=a(44493),o=a(29523),l=a(89667),c=a(80013),d=a(6211),m=a(63503),u=a(63213),x=a(62185),p=a(41862),h=a(99270),g=a(96474),f=a(93613),b=a(17313),v=a(62688);let j=(0,v.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var w=a(12597),N=a(13861),y=a(53411),_=a(63143);let C=(0,v.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var z=a(64021),A=a(84027),P=a(5336);function k(){let{user:e,isAuthenticated:t,isLoading:a}=(0,u.A)(),v=(0,n.useRouter)(),[k,E]=(0,s.useState)([]),[S,M]=(0,s.useState)(!0),[I,D]=(0,s.useState)(""),[F,$]=(0,s.useState)({}),[q,R]=(0,s.useState)(!1),[O,L]=(0,s.useState)(""),[G,J]=(0,s.useState)(!1),[T,B]=(0,s.useState)(!1),[Z,V]=(0,s.useState)(!1),[W,H]=(0,s.useState)(!1),[U,X]=(0,s.useState)(null),[K,Y]=(0,s.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[Q,ee]=(0,s.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[et,ea]=(0,s.useState)("change"),[er,es]=(0,s.useState)(""),[en,ei]=(0,s.useState)(!1),[eo,el]=(0,s.useState)(!1),[ec,ed]=(0,s.useState)({}),[em,eu]=(0,s.useState)({}),ex=async()=>{try{M(!0);let e=await x._I.getCantieri();E(e),await ep(e)}catch(e){D("Errore nel caricamento dei cantieri")}finally{M(!1)}},ep=async e=>{try{R(!0);let t=e.map(async e=>{try{let t=await x._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:t}}catch(t){return console.error(`Errore nel caricamento statistiche cantiere ${e.id_cantiere}:`,t),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),a=(await Promise.all(t)).reduce((e,{id:t,stats:a})=>(e[t]=a,e),{});$(a)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{R(!1)}},eh=async()=>{try{await x._I.createCantiere(K),J(!1),Y({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),ex()}catch(e){D("Errore nella creazione del cantiere")}},eg=async()=>{if(U)try{await x._I.updateCantiere(U.id_cantiere,K),B(!1),X(null),ex()}catch(e){D("Errore nella modifica del cantiere")}},ef=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),v.push(`/cantieri/${e.id_cantiere}`)},eb=e=>{X(e),Y({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),B(!0)},ev=async()=>{if(U){if(Q.newPassword!==Q.confirmPassword)return void D("Le password non coincidono");if(!Q.currentPassword)return void D("Inserisci la password attuale per confermare il cambio");if(!Q.newPassword||Q.newPassword.length<6)return void D("La nuova password deve essere di almeno 6 caratteri");try{M(!0),D("");let e=await fetch(`http://localhost:8001/api/cantieri/${U.id_cantiere}/change-password`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({password_attuale:Q.currentPassword,password_nuova:Q.newPassword,conferma_password:Q.confirmPassword})});if(!e.ok){let t=await e.json();throw Error(t.detail||"Errore nel cambio password")}let t=await e.json();if(t.success)ee({currentPassword:"",newPassword:"",confirmPassword:""}),V(!1),D(""),alert(t.message||"Password cambiata con successo");else throw Error(t.message||"Errore nel cambio password")}catch(e){D(e instanceof Error?e.message:"Errore nel cambio password")}finally{M(!1)}}},ej=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){}},ew=async e=>{let t=e.id_cantiere;if(ec[t])ed(e=>({...e,[t]:!1})),eu(e=>({...e,[t]:""}));else if(em[t])ed(e=>({...e,[t]:!0}));else try{el(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),a=await fetch(`http://localhost:8001/api/cantieri/${t}/view-password`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}});if(!a.ok){let e=await a.json();throw Error(e.detail||"Errore nel recupero password")}let r=await a.json();eu(e=>({...e,[t]:r.password_cantiere})),ed(e=>({...e,[t]:!0}))}catch(e){D(e instanceof Error?e.message:"Errore nel recupero password")}finally{el(!1)}},eN=k.filter(e=>e.commessa.toLowerCase().includes(O.toLowerCase())||e.descrizione?.toLowerCase().includes(O.toLowerCase())||e.nome_cliente?.toLowerCase().includes(O.toLowerCase()));return a?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}):(0,r.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsxs)("div",{className:"relative w-80",children:[(0,r.jsx)(h.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(l.p,{placeholder:"Cerca per commessa, descrizione o cliente...",value:O,onChange:e=>L(e.target.value),className:"pl-8 w-full"})]})}),(0,r.jsxs)(m.lG,{open:G,onOpenChange:J,children:[(0,r.jsx)(m.zM,{asChild:!0,children:(0,r.jsxs)(o.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,r.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(m.c7,{children:[(0,r.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,r.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,r.jsx)(l.p,{id:"commessa",value:K.commessa,onChange:e=>Y({...K,commessa:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,r.jsx)(l.p,{id:"descrizione",value:K.descrizione,onChange:e=>Y({...K,descrizione:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,r.jsx)(l.p,{id:"nome_cliente",value:K.nome_cliente,onChange:e=>Y({...K,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,r.jsx)(l.p,{id:"password_cantiere",type:"password",value:K.password_cantiere,onChange:e=>Y({...K,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,r.jsx)(m.Es,{children:(0,r.jsx)(o.$,{onClick:eh,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Crea Cantiere"})})]})]})]}),I&&(0,r.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,r.jsx)("span",{className:"text-red-800",children:I})]})}),S?(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}):0===eN.length?(0,r.jsx)(i.Zp,{children:(0,r.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,r.jsx)(b.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,r.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:O?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!O&&(0,r.jsxs)(o.$,{onClick:()=>J(!0),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,r.jsx)(i.Zp,{children:(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{children:(0,r.jsxs)(d.Hj,{className:"border-b border-gray-200",children:[(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Avanzamento"}),(0,r.jsx)(d.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,r.jsx)(d.nd,{className:"text-center font-semibold text-gray-700",children:"Azioni"})]})}),(0,r.jsx)(d.BF,{children:eN.map(e=>(0,r.jsxs)(d.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,r.jsx)(d.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,r.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,r.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,r.jsx)(d.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,r.jsx)(d.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>ej(e.codice_univoco),children:(0,r.jsx)(j,{className:"h-3 w-3"})})]})}),(0,r.jsx)(d.nA,{className:"py-4",children:(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsx)("div",{className:"flex items-center gap-2",children:ec[e.id_cantiere]&&em[e.id_cantiere]?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:em[e.id_cantiere]}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>ew(e),children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>ew(e),disabled:eo,children:eo?(0,r.jsx)(p.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(N.A,{className:"h-4 w-4"})})]})})})}),(0,r.jsx)(d.nA,{className:"py-4",children:(0,r.jsx)("div",{className:"flex items-center gap-2",children:q?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,r.jsx)("div",{className:`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${(F[e.id_cantiere]?.percentuale_avanzamento||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"}`,style:{width:`${Math.min(F[e.id_cantiere]?.percentuale_avanzamento||0,100)}%`}})})})})})}),(0,r.jsx)(d.nA,{className:"py-4 text-center",children:q?(0,r.jsx)(p.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,r.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("span",{className:`text-sm font-semibold ${(F[e.id_cantiere]?.percentuale_avanzamento||0)>=90?"text-green-700":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=75?"text-blue-700":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=50?"text-yellow-700":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=25?"text-orange-700":"text-red-700"}`,children:[(F[e.id_cantiere]?.percentuale_avanzamento||0).toFixed(1),"%"]})]})}),(0,r.jsx)(d.nA,{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>eb(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,r.jsxs)(o.$,{size:"sm",onClick:()=>ef(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200 ease-in-out shadow-sm hover:shadow-md hover:shadow-blue-500/25 transform hover:scale-105",title:"Accedi al cantiere",children:[(0,r.jsx)(C,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere))})]})}),(0,r.jsx)(m.lG,{open:T,onOpenChange:B,children:(0,r.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(m.c7,{children:[(0,r.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,r.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,r.jsx)(l.p,{id:"edit-commessa",value:K.commessa,onChange:e=>Y({...K,commessa:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,r.jsx)(l.p,{id:"edit-descrizione",value:K.descrizione,onChange:e=>Y({...K,descrizione:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,r.jsx)(l.p,{id:"edit-nome_cliente",value:K.nome_cliente,onChange:e=>Y({...K,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,r.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:K.indirizzo_cantiere,onChange:e=>Y({...K,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,r.jsx)(l.p,{id:"edit-citta_cantiere",value:K.citta_cantiere,onChange:e=>Y({...K,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,r.jsx)(l.p,{id:"edit-nazione_cantiere",value:K.nazione_cantiere,onChange:e=>Y({...K,nazione_cantiere:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(c.J,{className:"text-right",children:"Password"}),(0,r.jsx)("div",{className:"col-span-3",children:(0,r.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{V(!0)},className:"w-full h-10 justify-start text-left bg-gray-50 hover:bg-gray-100 border-gray-200 hover:border-gray-300 transition-colors",children:[(0,r.jsx)(z.A,{className:"h-4 w-4 mr-2 text-gray-500"}),(0,r.jsx)("span",{className:"text-gray-700",children:"Modifica Password"})]})})]})]}),(0,r.jsxs)(m.Es,{children:[(0,r.jsx)(o.$,{onClick:()=>B(!1),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Annulla"}),(0,r.jsx)(o.$,{onClick:eg,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Salva Modifiche"})]})]})}),(0,r.jsx)(m.lG,{open:Z,onOpenChange:e=>{V(e),e||(ee({currentPassword:"",newPassword:"",confirmPassword:""}),D(""))},children:(0,r.jsxs)(m.Cf,{className:"sm:max-w-[600px]",children:[(0,r.jsxs)(m.c7,{children:[(0,r.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,r.jsx)(z.A,{className:"h-5 w-5"}),"Gestione Password - ",U?.commessa]}),(0,r.jsx)(m.rr,{children:"Modifica la password di accesso al cantiere"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,r.jsx)(A.A,{className:"h-5 w-5"}),"Cambia Password"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci la password attuale e la nuova password"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"current-password-change",children:"Password Attuale"}),(0,r.jsx)(l.p,{id:"current-password-change",type:"password",placeholder:"Password attuale per conferma",value:Q.currentPassword,onChange:e=>ee({...Q,currentPassword:e.target.value})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"new-password",children:"Nuova Password"}),(0,r.jsx)(l.p,{id:"new-password",type:"password",placeholder:"Inserisci la nuova password",value:Q.newPassword,onChange:e=>ee({...Q,newPassword:e.target.value})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"confirm-password",children:"Conferma Nuova Password"}),(0,r.jsx)(l.p,{id:"confirm-password",type:"password",placeholder:"Conferma la nuova password",value:Q.confirmPassword,onChange:e=>ee({...Q,confirmPassword:e.target.value})})]}),(0,r.jsxs)(o.$,{onClick:ev,disabled:S||!Q.currentPassword||!Q.newPassword||!Q.confirmPassword,className:"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[S?(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})]})]}),I&&(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,r.jsx)("p",{className:"text-sm text-red-700 mt-1",children:I})]})]}),(0,r.jsx)(m.Es,{children:(0,r.jsx)(o.$,{variant:"outline",onClick:()=>V(!1),children:"Chiudi"})})]})}),(0,r.jsx)(m.lG,{open:W,onOpenChange:e=>{H(e),e||(es(""),X(null),D(""))},children:(0,r.jsxs)(m.Cf,{className:"sm:max-w-[500px]",children:[(0,r.jsxs)(m.c7,{children:[(0,r.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 text-green-600"}),"Password Cantiere - ",U?.commessa]}),(0,r.jsxs)(m.rr,{children:["Password per l'accesso al cantiere con codice: ",(0,r.jsx)("code",{className:"bg-muted px-2 py-1 rounded",children:U?.codice_univoco})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[er&&(0,r.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)(P.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsx)("span",{className:"font-medium text-green-800",children:"Password del Cantiere"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"flex-1 text-lg font-mono bg-white p-3 rounded border border-green-300 text-center",children:er}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>ej(er),className:"text-green-600 hover:bg-green-50 border-green-300",title:"Copia password",children:(0,r.jsx)(j,{className:"h-4 w-4"})})]}),(0,r.jsxs)("p",{className:"text-sm text-green-700 mt-2",children:["Utilizza questa password insieme al codice univoco ",(0,r.jsx)("strong",{children:U?.codice_univoco})," per accedere al cantiere."]})]}),I&&(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,r.jsx)("p",{className:"text-sm text-red-700 mt-1",children:I})]})]}),(0,r.jsx)(m.Es,{children:(0,r.jsx)(o.$,{variant:"outline",onClick:()=>H(!1),children:"Chiudi"})})]})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,a)=>{"use strict";a.d(t,{A0:()=>i,BF:()=>o,Hj:()=>l,XI:()=>n,nA:()=>d,nd:()=>c});var r=a(60687);a(43210);var s=a(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",e),...t})})}function i({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26134:(e,t,a)=>{"use strict";a.d(t,{UC:()=>ea,VY:()=>es,ZL:()=>ee,bL:()=>Y,bm:()=>en,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=a(43210),s=a(70569),n=a(98599),i=a(11273),o=a(96963),l=a(65551),c=a(31355),d=a(32547),m=a(25028),u=a(46059),x=a(14163),p=a(1359),h=a(42247),g=a(63376),f=a(8730),b=a(60687),v="Dialog",[j,w]=(0,i.A)(v),[N,y]=j(v),_=e=>{let{__scopeDialog:t,children:a,open:s,defaultOpen:n,onOpenChange:i,modal:c=!0}=e,d=r.useRef(null),m=r.useRef(null),[u,x]=(0,l.i)({prop:s,defaultProp:n??!1,onChange:i,caller:v});return(0,b.jsx)(N,{scope:t,triggerRef:d,contentRef:m,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:u,onOpenChange:x,onOpenToggle:r.useCallback(()=>x(e=>!e),[x]),modal:c,children:a})};_.displayName=v;var C="DialogTrigger",z=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,i=y(C,a),o=(0,n.s)(t,i.triggerRef);return(0,b.jsx)(x.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":V(i.open),...r,ref:o,onClick:(0,s.m)(e.onClick,i.onOpenToggle)})});z.displayName=C;var A="DialogPortal",[P,k]=j(A,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:a,children:s,container:n}=e,i=y(A,t);return(0,b.jsx)(P,{scope:t,forceMount:a,children:r.Children.map(s,e=>(0,b.jsx)(u.C,{present:a||i.open,children:(0,b.jsx)(m.Z,{asChild:!0,container:n,children:e})}))})};E.displayName=A;var S="DialogOverlay",M=r.forwardRef((e,t)=>{let a=k(S,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,n=y(S,e.__scopeDialog);return n.modal?(0,b.jsx)(u.C,{present:r||n.open,children:(0,b.jsx)(D,{...s,ref:t})}):null});M.displayName=S;var I=(0,f.TL)("DialogOverlay.RemoveScroll"),D=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=y(S,a);return(0,b.jsx)(h.A,{as:I,allowPinchZoom:!0,shards:[s.contentRef],children:(0,b.jsx)(x.sG.div,{"data-state":V(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),F="DialogContent",$=r.forwardRef((e,t)=>{let a=k(F,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,n=y(F,e.__scopeDialog);return(0,b.jsx)(u.C,{present:r||n.open,children:n.modal?(0,b.jsx)(q,{...s,ref:t}):(0,b.jsx)(R,{...s,ref:t})})});$.displayName=F;var q=r.forwardRef((e,t)=>{let a=y(F,e.__scopeDialog),i=r.useRef(null),o=(0,n.s)(t,a.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(O,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),R=r.forwardRef((e,t)=>{let a=y(F,e.__scopeDialog),s=r.useRef(!1),n=r.useRef(!1);return(0,b.jsx)(O,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(s.current||a.triggerRef.current?.focus(),t.preventDefault()),s.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(s.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let r=t.target;a.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),O=r.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,m=y(F,a),u=r.useRef(null),x=(0,n.s)(t,u);return(0,p.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(d.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,b.jsx)(c.qW,{role:"dialog",id:m.contentId,"aria-describedby":m.descriptionId,"aria-labelledby":m.titleId,"data-state":V(m.open),...l,ref:x,onDismiss:()=>m.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(X,{titleId:m.titleId}),(0,b.jsx)(K,{contentRef:u,descriptionId:m.descriptionId})]})]})}),L="DialogTitle",G=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=y(L,a);return(0,b.jsx)(x.sG.h2,{id:s.titleId,...r,ref:t})});G.displayName=L;var J="DialogDescription",T=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=y(J,a);return(0,b.jsx)(x.sG.p,{id:s.descriptionId,...r,ref:t})});T.displayName=J;var B="DialogClose",Z=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=y(B,a);return(0,b.jsx)(x.sG.button,{type:"button",...r,ref:t,onClick:(0,s.m)(e.onClick,()=>n.onOpenChange(!1))})});function V(e){return e?"open":"closed"}Z.displayName=B;var W="DialogTitleWarning",[H,U]=(0,i.q)(W,{contentName:F,titleName:L,docsSlug:"dialog"}),X=({titleId:e})=>{let t=U(W),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},K=({contentRef:e,descriptionId:t})=>{let a=U("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return r.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(s))},[s,e,t]),null},Y=_,Q=z,ee=E,et=M,ea=$,er=G,es=T,en=Z},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var r=a(60687);a(43210);var s=a(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},47386:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=a(65239),s=a(48088),n=a(88170),i=a.n(n),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c={children:["",{children:["cantieri",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,90910)),"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cantieri/page",pathname:"/cantieri",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>o,rr:()=>h,zM:()=>l});var r=a(60687);a(43210);var s=a(26134),n=a(11860),i=a(4780);function o({...e}){return(0,r.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function m({className:e,children:t,showCloseButton:a=!0,...o}){return(0,r.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,a&&(0,r.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function p({className:e,...t}){return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function h({className:e,...t}){return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},64021:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},65087:(e,t,a)=>{Promise.resolve().then(a.bind(a,90910))},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75863:(e,t,a)=>{Promise.resolve().then(a.bind(a,3070))},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(60687);a(43210);var s=a(78148),n=a(4780);function i({className:e,...t}){return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89667:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(60687);a(43210);var s=a(4780);function n({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},90910:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,757,658,841,223],()=>a(47386));module.exports=r})();